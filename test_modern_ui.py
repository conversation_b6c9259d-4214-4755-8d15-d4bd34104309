#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Modern UI - اختبار الواجهة العصرية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from modern_styles import ModernThemes, ModernFonts, ModernIcons, MODERN_COLORS
    from modern_widgets import ModernButton, ModernCard, ModernProgressBar, ModernStatusIndicator, ModernTooltip
    print("✨ Modern UI components loaded successfully!")
except ImportError as e:
    print(f"❌ Failed to load modern UI components: {e}")
    sys.exit(1)

class ModernUITest:
    def __init__(self):
        self.root = tk.Tk()
        self.theme_colors = ModernThemes.DARK_THEME
        self.setup_test_window()
        
    def setup_test_window(self):
        """Setup test window for modern UI"""
        self.root.title("🚀 Modern UI Test - Network Monitor 2025")
        self.root.geometry("800x600")
        self.root.configure(bg=self.theme_colors['bg_primary'])
        
        # Center window
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
        self.create_test_ui()
    
    def create_test_ui(self):
        """Create test UI elements"""
        # Header
        header = tk.Frame(self.root, bg=self.theme_colors['bg_secondary'], height=60)
        header.pack(fill='x', padx=0, pady=0)
        header.pack_propagate(False)
        
        title = tk.Label(
            header,
            text=f"{ModernIcons.ICONS['wifi']} Modern UI Test 2025",
            bg=self.theme_colors['bg_secondary'],
            fg=self.theme_colors['text_primary'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['heading'], 'bold')
        )
        title.pack(pady=15)
        
        # Main content
        main_frame = tk.Frame(self.root, bg=self.theme_colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Test card
        card = ModernCard(main_frame, title="🎨 Modern Components Test")
        card.pack(fill='x', pady=10)
        
        # Test buttons
        button_frame = tk.Frame(card, bg=self.theme_colors['bg_tertiary'])
        button_frame.pack(fill='x', padx=20, pady=10)
        
        # Different button styles
        btn1 = ModernButton(button_frame, text="Primary", icon=ModernIcons.ICONS['play'], 
                           style="primary", command=self.test_primary)
        btn1.pack(side='left', padx=5)
        
        btn2 = ModernButton(button_frame, text="Success", icon=ModernIcons.ICONS['online'], 
                           style="success", command=self.test_success)
        btn2.pack(side='left', padx=5)
        
        btn3 = ModernButton(button_frame, text="Danger", icon=ModernIcons.ICONS['blocked'], 
                           style="danger", command=self.test_danger)
        btn3.pack(side='left', padx=5)
        
        btn4 = ModernButton(button_frame, text="Secondary", icon=ModernIcons.ICONS['settings'], 
                           style="secondary", command=self.test_secondary)
        btn4.pack(side='left', padx=5)
        
        # Test progress bar
        progress_frame = tk.Frame(card, bg=self.theme_colors['bg_tertiary'])
        progress_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(progress_frame, text="📊 Progress Test:", 
                bg=self.theme_colors['bg_tertiary'], fg=self.theme_colors['text_primary']).pack(anchor='w')
        
        self.progress = ModernProgressBar(progress_frame, width=300, height=10)
        self.progress.pack(pady=5)
        
        # Test status indicators
        status_frame = tk.Frame(card, bg=self.theme_colors['bg_tertiary'])
        status_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(status_frame, text="🔄 Status Indicators:", 
                bg=self.theme_colors['bg_tertiary'], fg=self.theme_colors['text_primary']).pack(anchor='w')
        
        indicators_frame = tk.Frame(status_frame, bg=self.theme_colors['bg_tertiary'])
        indicators_frame.pack(fill='x', pady=5)
        
        # Different status indicators
        online_indicator = ModernStatusIndicator(indicators_frame, status="online", size=16)
        online_indicator.pack(side='left', padx=5)
        tk.Label(indicators_frame, text="Online", bg=self.theme_colors['bg_tertiary'], 
                fg=self.theme_colors['text_secondary']).pack(side='left', padx=5)
        
        offline_indicator = ModernStatusIndicator(indicators_frame, status="offline", size=16)
        offline_indicator.pack(side='left', padx=15)
        tk.Label(indicators_frame, text="Offline", bg=self.theme_colors['bg_tertiary'], 
                fg=self.theme_colors['text_secondary']).pack(side='left', padx=5)
        
        warning_indicator = ModernStatusIndicator(indicators_frame, status="warning", size=16)
        warning_indicator.pack(side='left', padx=15)
        tk.Label(indicators_frame, text="Warning", bg=self.theme_colors['bg_tertiary'], 
                fg=self.theme_colors['text_secondary']).pack(side='left', padx=5)
        
        # Test progress animation
        self.animate_progress()
        
        # Add tooltips
        ModernTooltip(btn1, "Primary action button")
        ModernTooltip(btn2, "Success state button")
        ModernTooltip(btn3, "Danger/warning button")
        ModernTooltip(btn4, "Secondary action button")
    
    def animate_progress(self):
        """Animate progress bar for demo"""
        import threading
        import time
        
        def animate():
            for i in range(101):
                self.progress.set_progress(i)
                time.sleep(0.05)
            # Reset and repeat
            self.root.after(2000, self.animate_progress)
        
        threading.Thread(target=animate, daemon=True).start()
    
    def test_primary(self):
        messagebox.showinfo("Test", "🔵 Primary button clicked!")
    
    def test_success(self):
        messagebox.showinfo("Test", "🟢 Success button clicked!")
    
    def test_danger(self):
        messagebox.showwarning("Test", "🔴 Danger button clicked!")
    
    def test_secondary(self):
        messagebox.showinfo("Test", "⚪ Secondary button clicked!")
    
    def run(self):
        """Run the test application"""
        print("🚀 Starting Modern UI Test...")
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = ModernUITest()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
