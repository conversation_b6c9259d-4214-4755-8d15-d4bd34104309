#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Eye-Friendly Themes - اختبار الثيمات المريحة للعين
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from modern_styles import ModernThemes, ModernFonts, ModernIcons, MODERN_COLORS
    from modern_widgets import ModernButton, ModernCard, ModernProgressBar, ModernStatusIndicator, ModernTooltip
    print("✨ Modern UI components loaded successfully!")
except ImportError as e:
    print(f"❌ Failed to load modern UI components: {e}")
    sys.exit(1)

class EyeFriendlyThemeTest:
    def __init__(self):
        self.root = tk.Tk()
        self.current_theme = "dark"
        self.themes = {
            "dark": ModernThemes.DARK_THEME,
            "light": ModernThemes.LIGHT_THEME,
            "sunset": ModernThemes.SUNSET_THEME,
            "ocean": ModernThemes.OCEAN_THEME
        }
        self.theme_colors = self.themes[self.current_theme]
        self.setup_test_window()
        
    def setup_test_window(self):
        """Setup test window for eye-friendly themes"""
        self.root.title("👁️ Eye-Friendly Themes Test 2025")
        self.root.geometry("1000x700")
        self.root.configure(bg=self.theme_colors['bg_primary'])
        
        # Center window
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
        self.create_test_ui()
    
    def create_test_ui(self):
        """Create test UI elements"""
        # Clear existing widgets
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Header
        header = tk.Frame(self.root, bg=self.theme_colors['bg_secondary'], height=80)
        header.pack(fill='x', padx=0, pady=0)
        header.pack_propagate(False)
        
        # Title
        title = tk.Label(
            header,
            text=f"👁️ Eye-Friendly Themes Test",
            bg=self.theme_colors['bg_secondary'],
            fg=self.theme_colors['text_primary'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['heading'], 'bold')
        )
        title.pack(side='left', padx=20, pady=25)
        
        # Theme selector
        theme_frame = tk.Frame(header, bg=self.theme_colors['bg_secondary'])
        theme_frame.pack(side='right', padx=20, pady=20)
        
        # Theme buttons
        themes_info = {
            "dark": ("🌙", "Dark"),
            "light": ("☀️", "Light"),
            "sunset": ("🌅", "Sunset"),
            "ocean": ("🌊", "Ocean")
        }
        
        for theme_key, (icon, name) in themes_info.items():
            style = "primary" if theme_key == self.current_theme else "secondary"
            btn = ModernButton(
                theme_frame,
                text=name,
                icon=icon,
                style=style,
                command=lambda t=theme_key: self.switch_theme(t)
            )
            btn.pack(side='left', padx=5)
        
        # Main content
        main_frame = tk.Frame(self.root, bg=self.theme_colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Theme info card
        info_card = tk.Frame(main_frame, bg=self.theme_colors['bg_tertiary'], relief='flat', borderwidth=1)
        info_card.pack(fill='x', pady=10)
        
        # Theme description
        theme_descriptions = {
            "dark": "🌙 Dark Theme - Warm purple-gray tones, easy on eyes for long use",
            "light": "☀️ Light Theme - Soft warm whites, comfortable for daytime",
            "sunset": "🌅 Sunset Theme - Ultra eye-friendly warm colors, perfect for evening",
            "ocean": "🌊 Ocean Theme - Cool calming blues, reduces eye strain"
        }
        
        desc_label = tk.Label(
            info_card,
            text=theme_descriptions[self.current_theme],
            bg=self.theme_colors['bg_tertiary'],
            fg=self.theme_colors['text_primary'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['lg']),
            wraplength=800
        )
        desc_label.pack(pady=15)
        
        # Color palette display
        palette_frame = tk.Frame(main_frame, bg=self.theme_colors['bg_primary'])
        palette_frame.pack(fill='x', pady=20)
        
        tk.Label(
            palette_frame,
            text="🎨 Color Palette:",
            bg=self.theme_colors['bg_primary'],
            fg=self.theme_colors['text_primary'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['lg'], 'bold')
        ).pack(anchor='w')
        
        # Color swatches
        colors_frame = tk.Frame(palette_frame, bg=self.theme_colors['bg_primary'])
        colors_frame.pack(fill='x', pady=10)
        
        color_info = [
            ("Primary", self.theme_colors['accent_primary']),
            ("Success", self.theme_colors['accent_success']),
            ("Warning", self.theme_colors['accent_warning']),
            ("Secondary", self.theme_colors['accent_secondary']),
            ("Background", self.theme_colors['bg_tertiary']),
            ("Text", self.theme_colors['text_primary'])
        ]
        
        for i, (name, color) in enumerate(color_info):
            swatch_frame = tk.Frame(colors_frame, bg=self.theme_colors['bg_primary'])
            swatch_frame.pack(side='left', padx=10)
            
            # Color swatch
            swatch = tk.Frame(swatch_frame, bg=color, width=60, height=40, relief='flat', borderwidth=1)
            swatch.pack()
            swatch.pack_propagate(False)
            
            # Color name
            tk.Label(
                swatch_frame,
                text=name,
                bg=self.theme_colors['bg_primary'],
                fg=self.theme_colors['text_secondary'],
                font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['sm'])
            ).pack(pady=5)
        
        # Sample content
        content_card = tk.Frame(main_frame, bg=self.theme_colors['bg_tertiary'], relief='flat', borderwidth=1)
        content_card.pack(fill='both', expand=True, pady=10)
        
        # Sample text
        sample_text = tk.Text(
            content_card,
            bg=self.theme_colors['bg_tertiary'],
            fg=self.theme_colors['text_primary'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['base']),
            height=8,
            wrap='word',
            relief='flat',
            borderwidth=0
        )
        sample_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        sample_content = f"""
🎨 Current Theme: {self.current_theme.title()}

This is a sample text to test readability and eye comfort. The colors have been carefully selected to:

• Reduce eye strain during long usage sessions
• Provide excellent contrast for text readability  
• Use warm, comfortable tones instead of harsh colors
• Maintain visual hierarchy with subtle color variations

👁️ Eye Comfort Features:
- Soft backgrounds instead of pure black/white
- Warm color temperatures
- Gentle accent colors
- Proper contrast ratios for accessibility

Try switching between themes to find your preferred color scheme!
        """
        
        sample_text.insert('1.0', sample_content)
        sample_text.config(state='disabled')
    
    def switch_theme(self, theme_name):
        """Switch to a different theme"""
        self.current_theme = theme_name
        self.theme_colors = self.themes[theme_name]
        self.root.configure(bg=self.theme_colors['bg_primary'])
        
        print(f"🎨 Switched to {theme_name.title()} Theme")
        self.create_test_ui()
    
    def run(self):
        """Run the test application"""
        print("👁️ Starting Eye-Friendly Themes Test...")
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = EyeFriendlyThemeTest()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
