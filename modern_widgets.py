import tkinter as tk
from tkinter import ttk
import threading
import time
from modern_styles import ModernThemes, ModernFonts, ModernIcons, MODERN_COLORS

class ModernButton(tk.But<PERSON>):
    """Modern button with hover effects and animations"""
    
    def __init__(self, parent, text="", icon="", style="primary", **kwargs):
        self.style_type = style
        self.icon = icon
        self.is_dark_theme = True  # Default to dark theme
        
        # Set theme colors
        theme = ModernThemes.DARK_THEME if self.is_dark_theme else ModernThemes.LIGHT_THEME
        
        # Style configurations
        styles = {
            'primary': {
                'bg': theme['accent_primary'],
                'fg': theme['text_primary'],
                'hover_bg': '#4A9EFF',
                'active_bg': '#2E8BFF'
            },
            'secondary': {
                'bg': theme['bg_tertiary'],
                'fg': theme['text_primary'],
                'hover_bg': theme['bg_hover'],
                'active_bg': theme['border']
            },
            'success': {
                'bg': theme['accent_success'],
                'fg': theme['text_primary'],
                'hover_bg': '#2EA043',
                'active_bg': '#238636'
            },
            'danger': {
                'bg': theme['accent_secondary'],
                'fg': theme['text_primary'],
                'hover_bg': '#E5484D',
                'active_bg': '#DC2626'
            }
        }
        
        current_style = styles.get(style, styles['primary'])
        
        # Combine icon and text
        display_text = f"{icon} {text}" if icon else text
        
        super().__init__(
            parent,
            text=display_text,
            bg=current_style['bg'],
            fg=current_style['fg'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['base'], 'normal'),
            relief='flat',
            borderwidth=0,
            padx=16,
            pady=8,
            cursor='hand2',
            **kwargs
        )
        
        self.default_bg = current_style['bg']
        self.hover_bg = current_style['hover_bg']
        self.active_bg = current_style['active_bg']
        
        # Bind hover events
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
        self.bind('<Button-1>', self._on_click)
        self.bind('<ButtonRelease-1>', self._on_release)
    
    def _on_enter(self, event):
        self.config(bg=self.hover_bg)
    
    def _on_leave(self, event):
        self.config(bg=self.default_bg)
    
    def _on_click(self, event):
        self.config(bg=self.active_bg)
    
    def _on_release(self, event):
        self.config(bg=self.hover_bg)

class ModernCard(tk.Frame):
    """Modern card component with shadow effect"""
    
    def __init__(self, parent, title="", **kwargs):
        self.is_dark_theme = True
        theme = ModernThemes.DARK_THEME if self.is_dark_theme else ModernThemes.LIGHT_THEME
        
        super().__init__(
            parent,
            bg=theme['bg_tertiary'],
            relief='flat',
            borderwidth=1,
            highlightbackground=theme['border'],
            highlightthickness=1,
            **kwargs
        )
        
        if title:
            title_label = tk.Label(
                self,
                text=title,
                bg=theme['bg_tertiary'],
                fg=theme['text_primary'],
                font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['lg'], 'bold')
            )
            title_label.pack(anchor='w', padx=16, pady=(16, 8))

class ModernProgressBar(tk.Canvas):
    """Modern animated progress bar"""
    
    def __init__(self, parent, width=200, height=8, **kwargs):
        self.is_dark_theme = True
        theme = ModernThemes.DARK_THEME if self.is_dark_theme else ModernThemes.LIGHT_THEME
        
        super().__init__(
            parent,
            width=width,
            height=height,
            bg=theme['bg_primary'],
            highlightthickness=0,
            **kwargs
        )
        
        self.width = width
        self.height = height
        self.progress = 0
        self.theme = theme
        
        # Draw background
        self.create_rectangle(
            0, 0, width, height,
            fill=theme['bg_secondary'],
            outline=theme['border'],
            width=1
        )
        
        # Progress bar
        self.progress_rect = self.create_rectangle(
            0, 0, 0, height,
            fill=theme['accent_primary'],
            outline=""
        )
    
    def set_progress(self, value):
        """Set progress value (0-100)"""
        self.progress = max(0, min(100, value))
        progress_width = (self.progress / 100) * self.width
        
        self.coords(self.progress_rect, 0, 0, progress_width, self.height)
        self.update()

class ModernStatusIndicator(tk.Canvas):
    """Modern status indicator with pulsing animation"""
    
    def __init__(self, parent, status="offline", size=12, **kwargs):
        super().__init__(
            parent,
            width=size,
            height=size,
            highlightthickness=0,
            **kwargs
        )
        
        self.size = size
        self.status = status
        self.is_animating = False
        
        # Status colors (updated for eye-friendly palette)
        self.colors = {
            'online': MODERN_COLORS['gentle_green'],
            'offline': MODERN_COLORS['soft_coral'],
            'warning': MODERN_COLORS['warm_orange'],
            'limited': MODERN_COLORS['warm_purple']
        }
        
        self.draw_indicator()
    
    def draw_indicator(self):
        self.delete("all")
        color = self.colors.get(self.status, self.colors['offline'])
        
        # Main circle
        self.create_oval(
            2, 2, self.size-2, self.size-2,
            fill=color,
            outline="",
            tags="indicator"
        )
        
        # Outer glow for online status
        if self.status == 'online':
            self.create_oval(
                0, 0, self.size, self.size,
                outline=color,
                width=1,
                tags="glow"
            )
    
    def set_status(self, status):
        self.status = status
        self.draw_indicator()
    
    def start_pulse(self):
        """Start pulsing animation for online status"""
        if not self.is_animating and self.status == 'online':
            self.is_animating = True
            self._pulse_animation()
    
    def _pulse_animation(self):
        if self.is_animating:
            # Simple pulse effect
            self.after(1000, self._pulse_animation)

class ModernTooltip:
    """Modern tooltip with smooth animations"""
    
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        
        self.widget.bind('<Enter>', self._on_enter)
        self.widget.bind('<Leave>', self._on_leave)
    
    def _on_enter(self, event):
        self._show_tooltip()
    
    def _on_leave(self, event):
        self._hide_tooltip()
    
    def _show_tooltip(self):
        if self.tooltip_window:
            return
        
        x = self.widget.winfo_rootx() + 20
        y = self.widget.winfo_rooty() + self.widget.winfo_height() + 5
        
        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")
        
        theme = ModernThemes.DARK_THEME
        
        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            bg=theme['bg_tertiary'],
            fg=theme['text_primary'],
            font=(ModernFonts.FONTS['primary'][0], ModernFonts.SIZES['sm']),
            relief='flat',
            borderwidth=1,
            padx=8,
            pady=4
        )
        label.pack()
    
    def _hide_tooltip(self):
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None
