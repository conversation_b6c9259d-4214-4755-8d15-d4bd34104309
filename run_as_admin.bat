@echo off
title Network Monitor - Administrator Mode
color 0A
echo ========================================
echo    🚀 Network Monitor - Admin Mode 🚀
echo ========================================
echo.
echo 🔍 Checking Administrator privileges...

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
    echo.
    echo 🚀 Starting Network Monitor with FULL FEATURES:
    echo    • Real Speed Control
    echo    • Internet Blocking
    echo    • Windows QoS Control
    echo    • Firewall Management
    echo.
    echo 🐍 Starting Python application...
    python network_monitor.py
    echo.
    echo 📊 Network Monitor closed.
    pause
) else (
    echo ❌ Not running as Administrator
    echo.
    echo ⚠️  LIMITED MODE - Some features disabled:
    echo    ❌ Speed Control
    echo    ❌ Internet Blocking
    echo    ✅ Network Monitoring only
    echo.
    echo 🔧 To enable ALL features:
    echo    Right-click this file → "Run as administrator"
    echo.
    echo 🚀 Requesting Administrator privileges...
    echo    Please click "Yes" when Windows asks for permission
    echo.
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && title Network Monitor - Admin Mode && color 0A && python network_monitor.py && echo. && echo Network Monitor closed. && pause' -Verb RunAs"
)
