@echo off
echo ========================================
echo    Network Monitor - Admin Mode
echo ========================================
echo.
echo Starting Network Monitor with Administrator privileges...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
    echo.
    echo Starting Python application...
    python network_monitor.py
    pause
) else (
    echo ❌ Not running as Administrator
    echo.
    echo Requesting Administrator privileges...
    echo Please click "Yes" when prompted
    echo.
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && python network_monitor.py && pause' -Verb RunAs"
)
