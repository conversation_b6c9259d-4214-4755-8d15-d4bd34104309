# Modern UI Styles for Network Monitor 2025
# 🎨 Trendy colors, animations, and modern design

class ModernThemes:
    """Modern UI themes with 2025 design trends"""
    
    # 🌙 Dark Theme (Primary)
    DARK_THEME = {
        'bg_primary': '#0D1117',      # GitHub dark
        'bg_secondary': '#161B22',    # Darker panels
        'bg_tertiary': '#21262D',     # Cards/buttons
        'bg_hover': '#30363D',        # Hover states
        'accent_primary': '#58A6FF',  # Blue accent
        'accent_secondary': '#F85149', # Red accent
        'accent_success': '#3FB950',  # Green success
        'accent_warning': '#D29922',  # Orange warning
        'text_primary': '#F0F6FC',    # Main text
        'text_secondary': '#8B949E',  # Secondary text
        'text_muted': '#6E7681',      # Muted text
        'border': '#30363D',          # Borders
        'shadow': 'rgba(0, 0, 0, 0.3)' # Shadows
    }
    
    # ☀️ Light Theme (Secondary)
    LIGHT_THEME = {
        'bg_primary': '#FFFFFF',      # Pure white
        'bg_secondary': '#F6F8FA',    # Light gray
        'bg_tertiary': '#FFFFFF',     # Cards/buttons
        'bg_hover': '#F3F4F6',        # Hover states
        'accent_primary': '#0969DA',  # Blue accent
        'accent_secondary': '#CF222E', # Red accent
        'accent_success': '#1A7F37',  # Green success
        'accent_warning': '#9A6700',  # Orange warning
        'text_primary': '#24292F',    # Main text
        'text_secondary': '#656D76',  # Secondary text
        'text_muted': '#8C959F',      # Muted text
        'border': '#D0D7DE',          # Borders
        'shadow': 'rgba(0, 0, 0, 0.1)' # Shadows
    }
    
    # 🎨 Gradient Themes
    GRADIENTS = {
        'primary': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'success': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'warning': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'danger': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        'info': 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)'
    }

class ModernFonts:
    """Modern font configurations"""
    
    FONTS = {
        'primary': ('Segoe UI', 'SF Pro Display', 'Inter', 'system-ui', 'sans-serif'),
        'secondary': ('Segoe UI Variable', 'SF Pro Text', 'Inter', 'system-ui'),
        'mono': ('Cascadia Code', 'SF Mono', 'Consolas', 'Monaco', 'monospace'),
        'arabic': ('Segoe UI', 'Tahoma', 'Arial Unicode MS', 'sans-serif')
    }
    
    SIZES = {
        'xs': 10,
        'sm': 11,
        'base': 12,
        'lg': 14,
        'xl': 16,
        'xxl': 18,
        'title': 20,
        'heading': 24
    }

class ModernIcons:
    """Modern icon set with Unicode symbols"""
    
    ICONS = {
        # Navigation & Actions
        'play': '▶️',
        'pause': '⏸️',
        'stop': '⏹️',
        'refresh': '🔄',
        'settings': '⚙️',
        'close': '✕',
        'minimize': '−',
        'maximize': '□',
        
        # Network & Devices
        'wifi': '📶',
        'ethernet': '🌐',
        'device': '💻',
        'mobile': '📱',
        'router': '📡',
        'server': '🖥️',
        
        # Data & Speed
        'download': '⬇️',
        'upload': '⬆️',
        'speed': '🚀',
        'data': '📊',
        'chart': '📈',
        'meter': '⚡',
        
        # Status
        'online': '🟢',
        'offline': '🔴',
        'warning': '🟡',
        'blocked': '🚫',
        'allowed': '✅',
        'limited': '⚠️',
        
        # File Operations
        'save': '💾',
        'load': '📁',
        'export': '📤',
        'import': '📥',
        'delete': '🗑️',
        'edit': '✏️',
        
        # UI Elements
        'menu': '☰',
        'search': '🔍',
        'filter': '🔽',
        'sort': '↕️',
        'expand': '⌄',
        'collapse': '⌃'
    }

class ModernAnimations:
    """CSS-like animation configurations for tkinter"""
    
    DURATIONS = {
        'fast': 150,      # ms
        'normal': 250,    # ms
        'slow': 400,      # ms
        'slower': 600     # ms
    }
    
    EASINGS = {
        'ease_in': 'ease-in',
        'ease_out': 'ease-out', 
        'ease_in_out': 'ease-in-out',
        'bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }

# 🎨 Modern color palette for 2025
MODERN_COLORS = {
    # Primary palette
    'electric_blue': '#007AFF',
    'cyber_purple': '#5856D6', 
    'neon_green': '#30D158',
    'sunset_orange': '#FF9500',
    'coral_red': '#FF3B30',
    
    # Neutral palette
    'space_gray': '#8E8E93',
    'silver': '#C7C7CC',
    'midnight': '#000000',
    'snow': '#FFFFFF',
    
    # Accent colors
    'mint': '#00C7BE',
    'lavender': '#AF52DE',
    'peach': '#FF6B35',
    'sky': '#007AFF',
    'forest': '#34C759'
}
