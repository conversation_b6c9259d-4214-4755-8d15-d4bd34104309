# Modern UI Styles for Network Monitor 2025
# 🎨 Trendy colors, animations, and modern design

class ModernThemes:
    """Modern UI themes with 2025 design trends"""
    
    # 🌙 Dark Theme (Eye-Friendly & Warm)
    DARK_THEME = {
        'bg_primary': '#1E1E2E',      # Warm dark purple-gray
        'bg_secondary': '#2A2A3E',    # Slightly lighter panels
        'bg_tertiary': '#363650',     # Cards/buttons - warmer
        'bg_hover': '#404057',        # Hover states - soft
        'accent_primary': '#74C0FC',  # Soft blue - easier on eyes
        'accent_secondary': '#F38BA8', # Soft pink instead of harsh red
        'accent_success': '#A6E3A1',  # Soft green - gentle
        'accent_warning': '#FAB387',  # Warm orange - comfortable
        'text_primary': '#CDD6F4',    # Soft white - not harsh
        'text_secondary': '#A6ADC8',  # Muted lavender
        'text_muted': '#6C7086',      # Gentle gray
        'border': '#45475A',          # Soft border
        'shadow': 'rgba(0, 0, 0, 0.2)' # Lighter shadow
    }
    
    # ☀️ Light Theme (Warm & Comfortable)
    LIGHT_THEME = {
        'bg_primary': '#FEFEFE',      # Warm white (not pure white)
        'bg_secondary': '#F5F5F7',    # Very light warm gray
        'bg_tertiary': '#FBFBFC',     # Cards/buttons - soft
        'bg_hover': '#F0F0F2',        # Hover states - gentle
        'accent_primary': '#4A90E2',  # Soft blue - not harsh
        'accent_secondary': '#E85D75', # Soft coral instead of red
        'accent_success': '#50C878',  # Emerald green - pleasant
        'accent_warning': '#FF9F43',  # Warm orange - friendly
        'text_primary': '#2C3E50',    # Dark blue-gray - easier to read
        'text_secondary': '#5A6C7D',  # Muted blue-gray
        'text_muted': '#8E9AAF',      # Soft gray-blue
        'border': '#E1E5E9',          # Very light border
        'shadow': 'rgba(0, 0, 0, 0.08)' # Very light shadow
    }

    # 🌅 Sunset Theme (Ultra Eye-Friendly)
    SUNSET_THEME = {
        'bg_primary': '#2D1B2E',      # Deep warm purple
        'bg_secondary': '#3D2A3E',    # Warmer secondary
        'bg_tertiary': '#4A3A4D',     # Soft purple-gray
        'bg_hover': '#5A4A5D',        # Gentle hover
        'accent_primary': '#FFB4A2',  # Warm coral
        'accent_secondary': '#E5989B', # Dusty rose
        'accent_success': '#B5E48C',  # Soft lime green
        'accent_warning': '#FFCF9E',  # Warm peach
        'text_primary': '#F2E9E4',    # Warm cream
        'text_secondary': '#C9ADA7',  # Soft beige
        'text_muted': '#A68A64',      # Warm brown
        'border': '#6D5A6E',          # Muted purple
        'shadow': 'rgba(0, 0, 0, 0.15)' # Soft shadow
    }

    # 🌊 Ocean Theme (Cool & Calming)
    OCEAN_THEME = {
        'bg_primary': '#1A2332',      # Deep ocean blue
        'bg_secondary': '#243447',    # Lighter ocean
        'bg_tertiary': '#2E455C',     # Blue-gray cards
        'bg_hover': '#385671',        # Soft blue hover
        'accent_primary': '#7DD3FC',  # Sky blue
        'accent_secondary': '#F472B6', # Soft magenta
        'accent_success': '#6EE7B7',  # Mint green
        'accent_warning': '#FBBF24',  # Golden yellow
        'text_primary': '#E0F2FE',    # Light cyan
        'text_secondary': '#BAE6FD',  # Soft blue
        'text_muted': '#7DD3FC',      # Medium blue
        'border': '#475569',          # Blue-gray border
        'shadow': 'rgba(0, 0, 0, 0.2)' # Ocean shadow
    }
    
    # 🎨 Gradient Themes
    GRADIENTS = {
        'primary': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'success': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'warning': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'danger': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        'info': 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)'
    }

class ModernFonts:
    """Modern font configurations"""
    
    FONTS = {
        'primary': ('Segoe UI', 'SF Pro Display', 'Inter', 'system-ui', 'sans-serif'),
        'secondary': ('Segoe UI Variable', 'SF Pro Text', 'Inter', 'system-ui'),
        'mono': ('Cascadia Code', 'SF Mono', 'Consolas', 'Monaco', 'monospace'),
        'arabic': ('Segoe UI', 'Tahoma', 'Arial Unicode MS', 'sans-serif')
    }
    
    SIZES = {
        'xs': 10,
        'sm': 11,
        'base': 12,
        'lg': 14,
        'xl': 16,
        'xxl': 18,
        'title': 20,
        'heading': 24
    }

class ModernIcons:
    """Modern icon set with Unicode symbols"""
    
    ICONS = {
        # Navigation & Actions
        'play': '▶️',
        'pause': '⏸️',
        'stop': '⏹️',
        'refresh': '🔄',
        'settings': '⚙️',
        'close': '✕',
        'minimize': '−',
        'maximize': '□',
        
        # Network & Devices
        'wifi': '📶',
        'ethernet': '🌐',
        'device': '💻',
        'mobile': '📱',
        'router': '📡',
        'server': '🖥️',
        
        # Data & Speed
        'download': '⬇️',
        'upload': '⬆️',
        'speed': '🚀',
        'data': '📊',
        'chart': '📈',
        'meter': '⚡',
        
        # Status
        'online': '🟢',
        'offline': '🔴',
        'warning': '🟡',
        'blocked': '🚫',
        'allowed': '✅',
        'limited': '⚠️',
        
        # File Operations
        'save': '💾',
        'load': '📁',
        'export': '📤',
        'import': '📥',
        'delete': '🗑️',
        'edit': '✏️',
        
        # UI Elements
        'menu': '☰',
        'search': '🔍',
        'filter': '🔽',
        'sort': '↕️',
        'expand': '⌄',
        'collapse': '⌃'
    }

class ModernAnimations:
    """CSS-like animation configurations for tkinter"""
    
    DURATIONS = {
        'fast': 150,      # ms
        'normal': 250,    # ms
        'slow': 400,      # ms
        'slower': 600     # ms
    }
    
    EASINGS = {
        'ease_in': 'ease-in',
        'ease_out': 'ease-out', 
        'ease_in_out': 'ease-in-out',
        'bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }

# 🎨 Eye-Friendly Color Palette 2025
MODERN_COLORS = {
    # Soft Primary palette (easier on eyes)
    'soft_blue': '#74C0FC',
    'warm_purple': '#CBA6F7',
    'gentle_green': '#A6E3A1',
    'warm_orange': '#FAB387',
    'soft_coral': '#F38BA8',

    # Comfortable Neutral palette
    'warm_gray': '#6C7086',
    'soft_silver': '#BAC2DE',
    'deep_charcoal': '#1E1E2E',
    'warm_white': '#F2E9E4',

    # Soothing Accent colors
    'mint_cream': '#94E2D5',
    'lavender_mist': '#DDB6F2',
    'peach_cream': '#FFCF9E',
    'sky_blue': '#89DCEB',
    'sage_green': '#B5E48C',

    # Special eye-comfort colors
    'sunset_pink': '#FFB4A2',
    'ocean_blue': '#7DD3FC',
    'forest_sage': '#6EE7B7',
    'golden_hour': '#F9E2AF'
}
